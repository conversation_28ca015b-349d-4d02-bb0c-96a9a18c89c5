import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/date_formatter.dart';
import '/src/core/services/locator.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/domain/models/agent_model.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsListScreen extends HookWidget {
  const AgentsListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get user from UserCubit
    final user = context.read<UserCubit>().state;
    if (user is! UserLoaded) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }

    // Initialize sorting state
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);

    // Use predefined headers from app_strings.dart (excluding agentStatus for now)
    final List<String> formattedHeaders = [
      agentName,
      agentContact,
      agentEmail,
      agentJoinDate,
      agentState,
      agentCity,
      agentLevel,
      agentTotalDeals,
      agentEarning,
      // agentStatus, // Commented out as it's not in API yet
    ];

    return BlocProvider(
      create: (context) {
        return AgentCubit(locator())..getAgents(
          userId: user.user!.userId, // Use userId from UserCubit
          page: 0,
          size: 20,
        );
      },
      child: BlocBuilder<AgentCubit, AgentState>(
        builder: (context, state) {
          List<AgentModel> agentData = [];
          bool isLoading = false;
          String? errorMessage;

          if (state is AgentLoading) {
            isLoading = true;
          } else if (state is AgentLoaded) {
            agentData = state.agents;
          } else if (state is AgentError) {
            errorMessage = state.message;
          }

          // Helper function to sort agents
          List<AgentModel> getSortedAgents() {
            if (agentData.isEmpty) return <AgentModel>[];

            final sorted = List<AgentModel>.from(agentData);
            if (sortColumn.value.isNotEmpty) {
              sorted.sort((a, b) {
                dynamic aValue, bValue;

                switch (sortColumn.value) {
                  case agentName:
                    aValue = a.fullName;
                    bValue = b.fullName;
                    break;
                  case agentContact:
                    aValue = a.phone;
                    bValue = b.phone;
                    break;
                  case agentEmail:
                    aValue = a.email;
                    bValue = b.email;
                    break;
                  case agentJoinDate:
                    aValue = a.joiningDate;
                    bValue = b.joiningDate;
                    break;
                  case agentState:
                    aValue = a.state;
                    bValue = b.state;
                    break;
                  case agentCity:
                    aValue = a.city;
                    bValue = b.city;
                    break;
                  case agentLevel:
                    aValue = a.role; // Using role as level
                    bValue = b.role;
                    break;
                  case agentTotalDeals:
                    aValue = a.salesMade; // agentTotalDeals = salesMade
                    bValue = b.salesMade;
                    break;
                  case agentEarning:
                    aValue = a.commissionEarnings; // agentEarning = commissionEarnings
                    bValue = b.commissionEarnings;
                    break;
                  default:
                    aValue = '';
                    bValue = '';
                }

                final comparison = aValue is num && bValue is num
                    ? aValue.compareTo(bValue)
                    : aValue is DateTime && bValue is DateTime
                    ? aValue.compareTo(bValue)
                    : aValue.toString().compareTo(bValue.toString());
                return sortAscending.value ? comparison : -comparison;
              });
            }
            return sorted;
          }

          void handleSort(String columnName, bool ascending) {
            sortColumn.value = columnName;
            sortAscending.value = ascending;
          }

          final sortedAgents = getSortedAgents();

          if (isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    errorMessage.contains(unauthorizedStatus) || errorMessage.contains(unauthorizedText)
                      ? Icons.lock_outline
                      : Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    errorMessage.contains(unauthorizedStatus) || errorMessage.contains(unauthorizedText)
                      ? authenticationRequired
                      : errorLoadingData,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage.contains(unauthorizedStatus) || errorMessage.contains(unauthorizedText)
                      ? pleaseLoginToAccessAgentData
                      : '$errorPrefix$errorMessage',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (context.mounted) {
                        context.read<AgentCubit>().getAgents(
                          userId: user.user!.userId, // Use dynamic userId
                          page: 0,
                          size: 20,
                        );
                      }
                    },
                    child: const Text(retry),
                  ),
                ],
              ),
            );
          }

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Customized table layout
              Flexible(
                child: CustomDataTableWidget<AgentModel>(
                  data: sortedAgents,
                  title: agents,
                  titleIcon: "$iconAssetpath/user.png",
                  searchHint: searchAgent,
                  searchFn: (agent) =>
                      agent.fullName +
                      agent.phone +
                      agent.email +
                      agent.state +
                      agent.city +
                      agent.role +
                      agent.salesMade.toString() +
                      agent.commissionEarnings.toString(),
                  // Dynamic filtering system
                  filterColumnNames: [
                    agentName, // name
                    agentState, // state
                    agentLevel, // level (role)
                    agentJoinDate, // join date
                  ],
                  filterValueExtractors: {
                    agentName: (agent) => agent.fullName,
                    agentState: (agent) => agent.state,
                    agentLevel: (agent) => agent.role,
                    agentJoinDate: (agent) => agent.joiningDate.toIso8601String(),
                  },
                  dateFilterColumns: const [
                    agentJoinDate, // Join date will use calendar picker
                  ],
                  columnNames: formattedHeaders,
                  cellBuilders: [
                    (agent) => agent.fullName, // name
                    (agent) => agent.phone, // contact
                    (agent) => agent.email, // email
                    (agent) => AppDateFormatter.formatDateMMddyyyy(agent.joiningDate), // joinDate
                    (agent) => agent.state, // state
                    (agent) => agent.city, // city
                    (agent) => agent.role, // level (using role)
                    (agent) => agent.salesMade.toString(), // totalDeals = salesMade
                    (agent) => '$currencySymbol${agent.commissionEarnings.toStringAsFixed(2)}', // earning = commissionEarnings
                    // (agent) => agent.status ? 'Active' : 'Inactive', // status - commented out as not in API
                  ],
                  iconCellBuilders: [
                    (agent) => TableCellData(
                      text: agent.fullName,
                      leftIconAsset: "$iconAssetpath/agent_round.png",
                      iconSize: 30,
                    ),
                    null, // contact - no icon
                    null, // email - no icon
                    null, // joinDate - no icon
                    null, // state - no icon
                    null, // city - no icon
                    null, // level - no icon
                    null, // totalDeals - no icon
                    null, // earning - no icon
                    // null, // status - no icon (commented out)
                  ],
                  useIconBuilders: [
                    true, // name - use icon
                    false, // contact - use text
                    false, // email - use text
                    false, // joinDate - use text
                    false, // state - use text
                    false, // city - use text
                    false, // level - use text
                    false, // totalDeals - use text
                    false, // earning - use text
                    // false, // status - use text (commented out)
                  ],
                  // Widget builders for styled cells
                  widgetCellBuilders: [
                    null, // name - use text
                    null, // contact - use text
                    null, // email - use text
                    null, // joinDate - use text
                    null, // state - use text
                    null, // city - use text
                    null, // level - use text
                    null, // totalDeals - use text
                    null, // earning - use text
                    // (context, agent) => Container(
                    //   // status - widget that fills cell width but maintains its own height
                    //   width: double.infinity, // Fill the entire cell width
                    //   // Small margin from cell edges
                    //   padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    //   decoration: BoxDecoration(
                    //     color: agent.status
                    //         ? AppTheme
                    //               .agentStatusActiveBg // Light green background
                    //         : AppTheme
                    //               .agentStatusInactiveBg, // Light red/pink background
                    //     borderRadius: BorderRadius.circular(
                    //       20,
                    //     ), 
                    //   )
                    // )
                  ],
                  // Boolean flags to indicate which columns use widget builders
                  useWidgetBuilders: [
                    false, // name - use text
                    false, // contact - use text
                    false, // email - use text
                    false, // joinDate - use text
                    false, // state - use text
                    false, // city - use text
                    false, // level - use text
                    false, // totalDeals - use text
                    false, // earning - use text
                    // false, // status - use text (commented out as not in API)
                  ],
                  actionBuilders: [
                    (context, agent) => ActionButtonEye(
                      onPressed: () => _onAgentAction(context, agent),
                      isCompact: true,
                      isMobile: false,
                    ),
                  ],
                  mobileCardBuilder: (context, agent) =>
                      _buildMobileAgentCard(agent, context),
                  onSort: handleSort,
                  emptyStateMessage: noDataAvailable,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('$actionClickedFor ${agent.fullName}')));
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(agent.fullName, style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.role == agentRoleValue
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.role == agentRoleValue ? active : inactive,
                  style: AppFonts.normalTextStyle(
                    12,
                    color: agent.role == agentRoleValue
                        ? AppTheme
                              .agentStatusActiveText // Darker green text
                        : AppTheme.agentStatusInactiveText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.phone}'),
          Text('$agentEmail: ${agent.email}'),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.role}'),
          Text('$agentTotalDeals: ${agent.salesMade}'),
          Text('$agentEarning: $currencySymbol${agent.commissionEarnings.toStringAsFixed(2)}'),
          Text(
            '$agentJoinDate: ${AppDateFormatter.formatDateMMddyyyy(agent.joiningDate)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
