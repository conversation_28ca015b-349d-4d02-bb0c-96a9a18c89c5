// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import '../../../core/config/json_consts.dart';
// import '/src/domain/models/broker.dart';
// import '../../../core/config/app_strings.dart' as AppStrings;
// import '../../../core/config/constants.dart';
// import '../../../core/config/responsive.dart';
// import '../../../core/theme/app_theme.dart';
// import '../../../core/theme/app_fonts.dart';
// import '../../../domain/models/agent.dart';
// import 'components/agent_hierarchy_breadcrumb.dart';
// import 'components/agent_network_card.dart';
// import 'components/agent_recruits_section.dart';

// class AgentNetworkScreen extends HookWidget {
//   final String selectedBrokerId;
//   final bool showScaffold;

//   AgentNetworkScreen({
//     super.key,
//     required this.selectedBrokerId,
//     this.showScaffold = true,
//   });

//   Broker? selectedBroker;

//   @override
//   Widget build(BuildContext context) {
//     if (showScaffold) {
//       return _buildWithScaffold(context);
//     } else {
//       return _buildContent(context);
//     }
//   }

//   Widget _buildWithScaffold(BuildContext context) {
//     final bool isMobile = Responsive.isMobile(context);

//     return Scaffold(
//       body: SafeArea(
//         child: Container(
//           padding: EdgeInsets.fromLTRB(
//             isMobile ? 8 : webLayoutmargin,
//             isMobile ? 8 : defaultMargin,
//             isMobile ? 8 : webLayoutmargin,
//             0,
//           ),
//           child: Column(children: [_buildContent(context)]),
//         ),
//       ),
//     );
//   }

//   Widget _buildContent(BuildContext context) {
//     final size = MediaQuery.of(context).size;
//     final selectedAgent = useState<Agent?>(null);
//     final hierarchyPath = useState<List<Agent>>([]);
//     final showAllRecruits = useState<bool>(false);

//     final selectedBrokerAgents = selectedBroker?.agents ?? [];

//     // Initialize with first agent from sample data
//     useEffect(() {
//       if (selectedBrokerId != '') {
//         ///TODO: change to api call
//         selectedBroker = brokersListJson.firstWhere(
//           (broker) => broker.id == selectedBrokerId,
//         );
//         selectedBrokerAgents.addAll(
//           brokersListJson
//               .firstWhere((broker) => broker.id == selectedBrokerId)
//               .agents,
//         );
//       }

//       if (selectedBrokerAgents.isNotEmpty && selectedAgent.value == null) {
//         selectedAgent.value = selectedBrokerAgents.first;
//       }
//       return null;
//     }, []);

//     void navigateToAgent(Agent agent) {
//       selectedAgent.value = agent;
//       // Add to hierarchy path if not already present
//       final currentPath = List<Agent>.from(hierarchyPath.value);
//       final existingIndex = currentPath.indexWhere((a) => a.name == agent.name);

//       if (existingIndex != -1) {
//         // If agent exists in path, truncate to that point
//         hierarchyPath.value = currentPath.sublist(0, existingIndex + 1);
//       } else {
//         // Add new agent to path
//         currentPath.add(agent);
//         hierarchyPath.value = currentPath;
//       }
//       showAllRecruits.value = false; // Reset on agent change
//     }

//     void navigateToLevel(int level) {
//       if (level < hierarchyPath.value.length) {
//         final newPath = hierarchyPath.value.sublist(0, level + 1);
//         hierarchyPath.value = newPath;
//         selectedAgent.value = newPath.last;
//         showAllRecruits.value = false; // Reset on level change
//       }
//     }

//     return Column(
//       children: [
//         AgentHierarchyBreadcrumb(
//           hierarchyPath: hierarchyPath.value,
//           onNavigate: navigateToLevel,
//           broker: selectedBroker,
//         ),
//         const SizedBox(height: defaultPadding),
//         Text(
//           AppStrings.agentNetwork,
//           style: AppFonts.semiBoldTextStyle(
//             24,
//             color: AppTheme.primaryTextColor,
//           ),
//         ),
//         const SizedBox(height: defaultPadding),
//         _networkView(
//           context,
//           hierarchyPath,
//           size,
//           selectedAgent,
//           showAllRecruits,
//           navigateToAgent,
//           useExpanded: false, // Don't use Expanded in content-only mode
//         ),
//       ],
//     );
//   }

//   Widget _networkView(
//     BuildContext context,
//     ValueNotifier<List<Agent>> hierarchyPath,
//     Size size,
//     ValueNotifier<Agent?> selectedAgent,
//     ValueNotifier<bool> showAllRecruits,
//     void Function(Agent agent) navigateToAgent, {
//     bool useExpanded = true,
//   }) {
//     Agent? agent = selectedAgent.value;
//     final isSmallMobile = Responsive.isSmallMobile(context);
//     final isMobile = Responsive.isMobile(context);
//     final isTablet = Responsive.isTablet(context);

//     final content = SingleChildScrollView(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           // Top broker card
//           if (hierarchyPath.value.isEmpty)
//             _buildAgentCard(
//               agent: agent,
//               isMainCard: true,
//               isBrokerCard: true,
//               useIntrinsic: true,
//             ),

//           if (hierarchyPath.value.isNotEmpty)
//             _buildAgentCard(
//               agent: agent,
//               isMainCard: false,
//               isBrokerCard: true,
//               width: isTablet
//                   ? size.width / 2.5
//                   : isMobile
//                   ? isSmallMobile
//                         ? size.width
//                         : size.width / 1.8
//                   : size.width / 4,
//               height: 100,
//             ),

//           _networkline(),

//           // Hierarchy cards
//           if (agent != null) ...[
//             for (final agentInPath in hierarchyPath.value)
//               Column(
//                 children: [
//                   _buildAgentCard(
//                     agent: agentInPath,
//                     isMainCard: agentInPath == hierarchyPath.value.last,
//                     isBrokerCard: false,
//                     useIntrinsic: agentInPath == hierarchyPath.value.last,
//                     width: agentInPath != hierarchyPath.value.last
//                         ? size.width / 4
//                         : null,
//                     height: agentInPath != hierarchyPath.value.last
//                         ? 100
//                         : null,
//                   ),
//                   _networkline(),
//                 ],
//               ),

//             // Recruits section
//             ValueListenableBuilder<bool>(
//               valueListenable: showAllRecruits,
//               builder: (context, showAll, _) {
//                 return AgentRecruitsSection(
//                   isBrokerLevel: hierarchyPath.value.isEmpty,
//                   broker: selectedBroker,
//                   parentAgent: agent,
//                   onAgentTap: navigateToAgent,
//                   showAll: showAll,
//                   onViewMore: () {
//                     showAllRecruits.value = !showAllRecruits.value;
//                   },
//                 );
//               },
//             ),
//           ],
//         ],
//       ),
//     );

//     return useExpanded ? Expanded(child: content) : content;
//   }

//   Widget _buildAgentCard({
//     required Agent? agent,
//     required bool isMainCard,
//     required bool isBrokerCard,
//     double? width,
//     double? height,
//     bool useIntrinsic = false,
//   }) {
//     final card = AgentNetworkCard(
//       broker: selectedBroker,
//       agent: agent,
//       isMainCard: isMainCard,
//       isBrokerCard: isBrokerCard,
//     );

//     if (useIntrinsic) {
//       return IntrinsicWidth(child: card);
//     }

//     if (width != null || height != null) {
//       return SizedBox(width: width, height: height, child: card);
//     }

//     return card;
//   }

//   Container _networkline() {
//     return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/models/agent.dart';
import '../../../domain/models/broker.dart';
import '../../cubit/cubit/agent_network_cubit.dart';
import 'components/agent_hierarchy_breadcrumb.dart';
import 'components/agent_network_card.dart';
import 'components/agent_recruits_section.dart';

class AgentNetworkScreen extends HookWidget {
  final String selectedBrokerId;
  final bool showScaffold;

  AgentNetworkScreen({
    super.key,
    required this.selectedBrokerId,
    this.showScaffold = true,
  });
  final ScrollController _scrollController1 = ScrollController();
  @override
  Widget build(BuildContext context) {
    if (showScaffold) {
      return _buildWithScaffold(context);
    } else {
      return _buildContent(context);
    }
  }

  Widget _buildWithScaffold(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);

    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            isMobile ? 8 : webLayoutmargin,
            isMobile ? 8 : defaultMargin,
            isMobile ? 8 : webLayoutmargin,
            0,
          ),
          child: SizedBox(
            height: MediaQuery.of(context).size.height, // Constrain height
            child: Column(children: [_buildContent(context)]),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final showAllRecruits = useState<bool>(false);
    final previousLengthRef = useRef(0);

    useEffect(() {
      // Reset hierarchy and fetch root level data
      context.read<AgentNetworkCubit>().resetHierarchy();
      context.read<AgentNetworkCubit>().fetchNetworkItems(
        selectedBrokerId,
        isRoot: true,
      );
      return null;
    }, [selectedBrokerId]);

    useEffect(() {
      final subscription = context.read<AgentNetworkCubit>().stream.listen((
        state,
      ) {
        if (state is AgentNetworkSuccess) {
          final currentLength = state.hierarchyPath.length;

          if (currentLength > previousLengthRef.value) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scrollController1.hasClients) {
                _scrollController1.animateTo(
                  _scrollController1.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            });
          }

          previousLengthRef.value = currentLength;
        }
      });

      return subscription.cancel;
    }, []);

    void navigateToAgent(Agent agent) {
      context.read<AgentNetworkCubit>().navigateToAgent(agent);
      showAllRecruits.value = false; // Reset on agent change
    }

    void navigateToLevel(int level) {
      context.read<AgentNetworkCubit>().navigateToLevel(level);
      showAllRecruits.value = false; // Reset on level change
    }

    return SizedBox(
      height: size.height, // Constrain height

      child: BlocListener<AgentNetworkCubit, AgentNetworkState>(
        listener: (context, state) {
          if (state is AgentNetworkSuccess) {
            final currentLength = state.hierarchyPath.length;

            if (currentLength > previousLengthRef.value) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_scrollController1.hasClients) {
                  _scrollController1.animateTo(
                    _scrollController1.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              });
            }

            previousLengthRef.value = currentLength;
          }
        },

        child: BlocBuilder<AgentNetworkCubit, AgentNetworkState>(
          builder: (context, state) {
            debugPrint('State: $state');

            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(0.0, 0.02),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                          ),
                        ),
                    child: child,
                  ),
                );
              },
              child: _buildStateContent(
                context,
                state,
                navigateToAgent,
                navigateToLevel,
                showAllRecruits,
                size,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStateContent(
    BuildContext context,
    AgentNetworkState state,
    Function(Agent) navigateToAgent,
    Function(int) navigateToLevel,
    ValueNotifier<bool> showAllRecruits,
    Size size,
  ) {
    if (state is AgentNetworkLoading) {
      return Container(
        key: const ValueKey('loading'),
        child: const Center(child: CircularProgressIndicator()),
      );
    } else if (state is AgentNetworkError) {
      return Container(
        key: const ValueKey('error'),
        child: Center(child: Text('Failed to load agents: ${state.message}')),
      );
    } else if (state is AgentNetworkSuccess) {
      final selectedAgent = state.agent;
      final broker = state.broker;
      final hierarchyPath = state.hierarchyPath;

      if (selectedAgent == null || broker == null) {
        return Container(
          key: const ValueKey('no-data'),
          child: const Center(child: Text('No agent or broker data available')),
        );
      }

      return Container(
        key: ValueKey(
          'success-${selectedAgent.id}',
        ), // Unique key for each agent
        child: Column(
          children: [
            // Breadcrumb with smooth animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: AgentHierarchyBreadcrumb(
                hierarchyPath: hierarchyPath,
                onNavigate: navigateToLevel,
                broker: broker,
              ),
            ),
            const SizedBox(height: defaultPadding),
            Text(
              AppStrings.agentNetwork,
              style: AppFonts.semiBoldTextStyle(
                24,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: defaultPadding),
            // Wrap in Expanded to prevent overflow with deep hierarchies
            Expanded(
              child: _networkView(
                context,
                hierarchyPath,
                size,
                selectedAgent,
                showAllRecruits,
                navigateToAgent,
                useExpanded: true, // Enable expanded mode for proper scrolling
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      key: const ValueKey('initializing'),
      child: const Center(child: Text('Initializing...')),
    );
  }

  Widget _networkView(
    BuildContext context,
    List<Agent> hierarchyPath,
    Size size,
    Agent? agent,
    ValueNotifier<bool> showAllRecruits,
    void Function(Agent agent) navigateToAgent, {
    bool useExpanded = false,
  }) {
    final isSmallMobile = Responsive.isSmallMobile(context);
    final isMobile = Responsive.isMobile(context);
    final isTablet = Responsive.isTablet(context);

    // Calculate available height for deep hierarchies
    final availableHeight =
        size.height - 200; // Reserve space for header/breadcrumb
    final hierarchyDepth = hierarchyPath.length;
    final isDeepHierarchy = hierarchyDepth > 3;

    final content = SingleChildScrollView(
      controller: _scrollController1,
      physics: const BouncingScrollPhysics(),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: isDeepHierarchy ? availableHeight : 0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (hierarchyPath.isEmpty)
              _buildAgentCard(
                context: context,
                agent: agent,
                isMainCard: true,
                isBrokerCard: true,
                useIntrinsic: true,
              ),

            if (hierarchyPath.isNotEmpty)
              _buildAgentCard(
                context: context,
                agent: agent,
                isMainCard: false,
                isBrokerCard: true,
                width: isTablet
                    ? size.width / 2.5
                    : isMobile
                    ? isSmallMobile
                          ? size.width
                          : size.width / 1.8
                    : size.width / 4,
                height: 100,
              ),

            _networkline(),

            if (agent != null) ...[
              for (int index = 0; index < hierarchyPath.length; index++)
                _AnimatedHierarchyCard(
                  key: ValueKey('hierarchy-${hierarchyPath[index].id}'),
                  agent: hierarchyPath[index],
                  isMainCard: index == hierarchyPath.length - 1,
                  size: size,
                  animationDelay: Duration(milliseconds: index * 100),
                ),

              ValueListenableBuilder<bool>(
                valueListenable: showAllRecruits,
                builder: (context, showAll, _) {
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeOutCubic,
                    child: _AnimatedAgentRecruitsSection(
                      key: ValueKey('recruits-${agent?.id}'),
                      isBrokerLevel: hierarchyPath.isEmpty,
                      broker:
                          context.read<AgentNetworkCubit>().state
                              is AgentNetworkSuccess
                          ? (context.read<AgentNetworkCubit>().state
                                    as AgentNetworkSuccess)
                                .broker
                          : null,
                      parentAgent: agent,
                      onAgentTap: navigateToAgent,
                      showAll: showAll,
                      onViewMore: () {
                        showAllRecruits.value = !showAllRecruits.value;
                        debugPrint(
                          'showAllRecruits toggled to: ${showAllRecruits.value}',
                        );
                      },
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );

    return content;
  }

  Widget _buildAgentCard({
    required BuildContext context,
    required Agent? agent,
    required bool isMainCard,
    required bool isBrokerCard,
    double? width,
    double? height,
    bool useIntrinsic = false,
  }) {
    final card = AgentNetworkCard(
      broker: context.read<AgentNetworkCubit>().state is AgentNetworkSuccess
          ? (context.read<AgentNetworkCubit>().state as AgentNetworkSuccess)
                .broker
          : null,
      agent: agent,
      isMainCard: isMainCard,
      isBrokerCard: isBrokerCard,
    );

    if (useIntrinsic) {
      return IntrinsicWidth(child: card);
    }

    if (width != null || height != null) {
      return SizedBox(width: width, height: height, child: card);
    }

    return card;
  }

  Container _networkline() {
    return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
  }
}

// Animated hierarchy card for smooth hierarchy transitions
class _AnimatedHierarchyCard extends StatefulWidget {
  final Agent agent;
  final bool isMainCard;
  final Size size;
  final Duration animationDelay;

  const _AnimatedHierarchyCard({
    super.key,
    required this.agent,
    required this.isMainCard,
    required this.size,
    this.animationDelay = Duration.zero,
  });

  @override
  State<_AnimatedHierarchyCard> createState() => _AnimatedHierarchyCardState();
}

class _AnimatedHierarchyCardState extends State<_AnimatedHierarchyCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimation();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0.0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.9, curve: Curves.easeOutQuart),
          ),
        );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutBack),
      ),
    );
  }

  void _startAnimation() {
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);
    final isMobile = Responsive.isMobile(context);
    final isTablet = Responsive.isTablet(context);

    // Make cards more compact for deep hierarchies
    final isDeepHierarchy =
        widget.animationDelay.inMilliseconds > 300; // More than 3 levels
    final cardHeight = isDeepHierarchy
        ? 80.0
        : (widget.isMainCard ? null : 100.0);
    final lineHeight = isDeepHierarchy ? 20.0 : 30.0;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAgentCard(
                    context: context,
                    agent: widget.agent,
                    isMainCard: widget.isMainCard,
                    isBrokerCard: false,
                    useIntrinsic: widget.isMainCard,
                    width: !widget.isMainCard ? widget.size.width / 4 : null,
                    height: cardHeight,
                  ),
                  Container(
                    height: lineHeight,
                    width: 1,
                    color: AppTheme.hierarchyLineColor,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAgentCard({
    required BuildContext context,
    required Agent? agent,
    required bool isMainCard,
    required bool isBrokerCard,
    double? width,
    double? height,
    bool useIntrinsic = false,
  }) {
    final card = AgentNetworkCard(
      broker: context.read<AgentNetworkCubit>().state is AgentNetworkSuccess
          ? (context.read<AgentNetworkCubit>().state as AgentNetworkSuccess)
                .broker
          : null,
      agent: agent,
      isMainCard: isMainCard,
      isBrokerCard: isBrokerCard,
    );

    if (useIntrinsic) {
      return IntrinsicWidth(child: card);
    }

    if (width != null || height != null) {
      return SizedBox(width: width, height: height, child: card);
    }

    return card;
  }
}

// Animated wrapper for AgentRecruitsSection to handle smooth transitions
class _AnimatedAgentRecruitsSection extends StatefulWidget {
  final bool isBrokerLevel;
  final Broker? broker;
  final Agent? parentAgent;
  final Function(Agent) onAgentTap;
  final bool showAll;
  final VoidCallback? onViewMore;

  const _AnimatedAgentRecruitsSection({
    super.key,
    required this.isBrokerLevel,
    required this.parentAgent,
    required this.onAgentTap,
    this.showAll = false,
    this.onViewMore,
    required this.broker,
  });

  @override
  State<_AnimatedAgentRecruitsSection> createState() =>
      _AnimatedAgentRecruitsSectionState();
}

class _AnimatedAgentRecruitsSectionState
    extends State<_AnimatedAgentRecruitsSection>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0.0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void didUpdateWidget(_AnimatedAgentRecruitsSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.parentAgent?.id != widget.parentAgent?.id) {
      // Reset and restart animations when parent agent changes
      _fadeController.reset();
      _slideController.reset();
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.parentAgent == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_slideController, _fadeController]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: AgentRecruitsSection(
                isBrokerLevel: widget.isBrokerLevel,
                broker: widget.broker,
                parentAgent: widget.parentAgent!,
                onAgentTap: _handleAgentTap,
                showAll: widget.showAll,
                onViewMore: widget.onViewMore,
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleAgentTap(Agent agent) {
    // Add a subtle animation before navigating
    _fadeController.reverse().then((_) {
      widget.onAgentTap(agent);
    });
  }
}
