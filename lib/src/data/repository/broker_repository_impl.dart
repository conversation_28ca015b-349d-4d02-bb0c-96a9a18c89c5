import 'package:dio/dio.dart';
import '/src/domain/models/broker_api.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/repository/broker_repository.dart';

class BrokerRepositoryImpl extends BrokerRepository {
  BrokerRepositoryImpl();

  static final String brokersUrl = APIConfig.brokers;

  @override
  Future<BrokerApi?> getBrokers(Map<String, dynamic> payload) async {
    try {
      print(payload);
      final dio = await DioClient.getDio();
      final response = await dio.post(brokersUrl, data: payload);

      if (response.statusCode == 200) {
        final Map<String, dynamic> brokerDataList = response.data ?? {};
        final brokers = BrokerApi.from<PERSON>son(brokerDataList);
        return brokers;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchBrokers);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
